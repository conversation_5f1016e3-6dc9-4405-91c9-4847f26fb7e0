import React, { useState } from "react";
import {
  But<PERSON>,
  Segment,
  Label,
  Grid,
  Header,
  Divider,
  Icon,
  Popup,
  Modal,
} from "semantic-ui-react";
import { toast } from "react-toastify";
import {
  functionAddAuthClaimAssistant,
  functionAddAuthClaimManagerAssistant,
  functionAddAuthClaimRole,
  functionCreateNewUser,
  functionCreateViewInDataFiniti,
  functionDelAuthClaimAssistant,
  functionDelAuthClaimManagerAssistant,
  functionFetchViewsFromDataFiniti,
  functionImpersonateUser,
  functionUpdateUser,
  functionUpdateUserEmail,
  functionViewAllUsersWithCustomClaims,
  functionViewAuthClaimRole,
} from "../../app/firestore/functionsService";
import MyTextInput from "../../app/common/form/MyTextInput";
import * as Yup from "yup";
import { Form, Formik, useFormikContext } from "formik";
import MySelectInput from "../../app/common/form/MySelectInput";
import MyMultiSelectInput from "../../app/common/form/MyMultiSelectInput";
import { signInCustomAuth } from "../../app/firestore/firebaseService";
import { useDispatch, useSelector } from "react-redux";
import { openModal } from "../../app/common/modals/modalSlice";
import {
  getFormFieldConversionsFromDb,
  getUserProfileByEmailFromDb,
  getUserProfileFromDb,
  updateProfileFieldsInDb,
  fetchAllManagersFromDb,
} from "../../app/firestore/firestoreService";
import MyEmailInput from "../../app/common/form/MyEmailInput";
import FormAddress from "../../app/common/form/FormAddress";
import { useMediaQuery } from "react-responsive";
import MyRadioButtons from "../../app/common/form/MyRadioButtons";

import {
  getFirestore,
  collection,
  query,
  where,
  getDocs,
  writeBatch,
  doc,
  getDoc,
} from "firebase/firestore";
import { app } from "../../app/config/firebase";
import { Input, Table } from "semantic-ui-react";
import ExpireFormSelect from "./ExpireFormSelect";
import CsvUserUpload from "./CsvUserUpload";

const db = getFirestore(app);

const FormAction = {
  authView: "view",
  authSet: "set",
  authAssist: "assist",
  authDeleteAssist: "deleteAssist",
  authNone: "none",
};

const ROLE_TYPE_KEYS = {
  ADMIN: "admin",
  TC: "tc",
  ASSISTANT: "assistant",
  MANAGING_BROKER: "managingbroker",
  MANAGER_ASSISTANT: "managerassistant", // New role key
};
const ROLE_TYPE_VALUES = {
  ADMIN: "d",
  TC: "t",
  ASSISTANT: "a",
  MANAGING_BROKER: "m",
  MANAGER_ASSISTANT: "g", // New role value
};

let roleOptions = [
  { key: "", value: "", text: "" },
  {
    key: ROLE_TYPE_KEYS.ADMIN,
    value: ROLE_TYPE_VALUES.ADMIN,
    text: "Admin",
  },
  {
    key: ROLE_TYPE_KEYS.TC,
    value: ROLE_TYPE_VALUES.TC,
    text: "TC",
  },
  {
    key: ROLE_TYPE_KEYS.ASSISTANT,
    value: ROLE_TYPE_VALUES.ASSISTANT,
    text: "Assistant",
  },
  {
    key: ROLE_TYPE_KEYS.MANAGING_BROKER,
    value: ROLE_TYPE_VALUES.MANAGING_BROKER,
    text: "Managing Broker",
  },
  {
    key: ROLE_TYPE_KEYS.MANAGER_ASSISTANT,
    value: ROLE_TYPE_VALUES.MANAGER_ASSISTANT,
    text: "Manager Assistant",
  },
];

let userRoleOptions = [
  {
    key: "agent",
    value: "agent",
    text: "agent",
  },
  {
    key: "assistant",
    value: "assistant",
    text: "assistant",
  },
  {
    key: "coagent",
    value: "coagent",
    text: "coagent",
  },
  {
    key: "manager",
    value: "manager",
    text: "manager",
  },
  {
    key: "tc",
    value: "tc",
    text: "tc",
  },
  {
    key: "managerassistant",
    value: "managerassistant",
    text: "managerassistant",
  },
];

let userStateOptions = [
  {
    key: "Colorado",
    value: "Colorado",
    text: "Colorado",
  },
  {
    key: "Louisiana",
    value: "Louisiana",
    text: "Louisiana",
  },
  {
    key: "Oklahoma",
    value: "Oklahoma",
    text: "Oklahoma",
  },
  {
    key: "Texas",
    value: "Texas",
    text: "Texas",
  },
  {
    key: "Arizona",
    value: "Arizona",
    text: "Arizona",
  },
  {
    key: "California",
    value: "California",
    text: "California",
  },
];

// State/Province options with display names and codes
const stateProvinceOptions = [
  { displayName: "Alabama", stateProvinceCode: "AL" },
  { displayName: "Alaska", stateProvinceCode: "AK" },
  { displayName: "Arizona", stateProvinceCode: "AZ" },
  { displayName: "Arkansas", stateProvinceCode: "AR" },
  { displayName: "California", stateProvinceCode: "CA" },
  { displayName: "Colorado", stateProvinceCode: "CO" },
  { displayName: "Connecticut", stateProvinceCode: "CT" },
  { displayName: "Delaware", stateProvinceCode: "DE" },
  { displayName: "Florida", stateProvinceCode: "FL" },
  { displayName: "Georgia", stateProvinceCode: "GA" },
  { displayName: "Hawaii", stateProvinceCode: "HI" },
  { displayName: "Idaho", stateProvinceCode: "ID" },
  { displayName: "Illinois", stateProvinceCode: "IL" },
  { displayName: "Indiana", stateProvinceCode: "IN" },
  { displayName: "Iowa", stateProvinceCode: "IA" },
  { displayName: "Kansas", stateProvinceCode: "KS" },
  { displayName: "Kentucky", stateProvinceCode: "KY" },
  { displayName: "Louisiana", stateProvinceCode: "LA" },
  { displayName: "Maine", stateProvinceCode: "ME" },
  { displayName: "Maryland", stateProvinceCode: "MD" },
  { displayName: "Massachusetts", stateProvinceCode: "MA" },
  { displayName: "Michigan", stateProvinceCode: "MI" },
  { displayName: "Minnesota", stateProvinceCode: "MN" },
  { displayName: "Mississippi", stateProvinceCode: "MS" },
  { displayName: "Missouri", stateProvinceCode: "MO" },
  { displayName: "Montana", stateProvinceCode: "MT" },
  { displayName: "Nebraska", stateProvinceCode: "NE" },
  { displayName: "Nevada", stateProvinceCode: "NV" },
  { displayName: "New Hampshire", stateProvinceCode: "NH" },
  { displayName: "New Jersey", stateProvinceCode: "NJ" },
  { displayName: "New Mexico", stateProvinceCode: "NM" },
  { displayName: "New York", stateProvinceCode: "NY" },
  { displayName: "North Carolina", stateProvinceCode: "NC" },
  { displayName: "North Dakota", stateProvinceCode: "ND" },
  { displayName: "Ohio", stateProvinceCode: "OH" },
  { displayName: "Oklahoma", stateProvinceCode: "OK" },
  { displayName: "Oregon", stateProvinceCode: "OR" },
  { displayName: "Pennsylvania", stateProvinceCode: "PA" },
  { displayName: "Rhode Island", stateProvinceCode: "RI" },
  { displayName: "South Carolina", stateProvinceCode: "SC" },
  { displayName: "South Dakota", stateProvinceCode: "SD" },
  { displayName: "Tennessee", stateProvinceCode: "TN" },
  { displayName: "Texas", stateProvinceCode: "TX" },
  { displayName: "Utah", stateProvinceCode: "UT" },
  { displayName: "Vermont", stateProvinceCode: "VT" },
  { displayName: "Virginia", stateProvinceCode: "VA" },
  { displayName: "Washington", stateProvinceCode: "WA" },
  { displayName: "West Virginia", stateProvinceCode: "WV" },
  { displayName: "Wisconsin", stateProvinceCode: "WI" },
  { displayName: "Wyoming", stateProvinceCode: "WY" },
  // Canadian Provinces
  { displayName: "Alberta", stateProvinceCode: "AB" },
  { displayName: "British Columbia", stateProvinceCode: "BC" },
  { displayName: "Manitoba", stateProvinceCode: "MB" },
  { displayName: "New Brunswick", stateProvinceCode: "NB" },
  { displayName: "Newfoundland and Labrador", stateProvinceCode: "NL" },
  { displayName: "Northwest Territories", stateProvinceCode: "NT" },
  { displayName: "Nova Scotia", stateProvinceCode: "NS" },
  { displayName: "Nunavut", stateProvinceCode: "NU" },
  { displayName: "Ontario", stateProvinceCode: "ON" },
  { displayName: "Prince Edward Island", stateProvinceCode: "PE" },
  { displayName: "Quebec", stateProvinceCode: "QC" },
  { displayName: "Saskatchewan", stateProvinceCode: "SK" },
  { displayName: "Yukon", stateProvinceCode: "YT" },
];

// Convert to dropdown options format
const stateProvinceDropdownOptions = stateProvinceOptions.map((option) => ({
  key: option.stateProvinceCode,
  value: option.stateProvinceCode,
  text: option.displayName,
}));

// Type options for user type dropdown
const userTypeOptions = [
  { key: "user", value: "user", text: "User" },
  { key: "party", value: "party", text: "Party" },
];
let userCitiesOptions = [
  {
    key: "",
    value: "",
    text: "",
  },
  {
    key: "Avon OR Glenwood Springs OR Beaver Creek OR Edwards OR West Vail OR Minturn OR Eagles Nest OR Dowds Junction OR Vail OR Eagle Vail OR Red Cliff OR Copper Mountain",
    value:
      "Avon OR Glenwood Springs OR Beaver Creek OR Edwards OR West Vail OR Minturn OR Eagles Nest OR Dowds Junction OR Vail OR Eagle Vail OR Red Cliff OR Copper Mountain",
    text: "Vail / Copper / Glenwood Springs",
  },
  {
    key: "Colorado Springs OR Pueblo OR Monument OR Fountain OR Woodland Park OR Manitou Springs OR Florissant OR Falcon OR Larksburg OR Palmer Lake OR Elbert OR Cripple Creek OR Green Mountain Falls",
    value:
      "Colorado Springs OR Pueblo OR Monument OR Fountain OR Woodland Park OR Manitou Springs OR Florissant OR Falcon OR Larksburg OR Palmer Lake OR Elbert OR Cripple Creek OR Green Mountain Falls",
    text: "Colorado Springs / Pueblo",
  },

  {
    key: "Canon City OR Penrose OR Florence OR Wetmore OR Pueblo West OR Pueblo OR Westcliffe OR Texas Creek OR Hillside OR Guffey OR Coaldale OR Howard",
    value:
      "Canon City OR Penrose OR Florence OR Wetmore OR Pueblo West OR Pueblo OR Westcliffe OR Texas Creek OR Hillside OR Guffey OR Coaldale OR Howard",
    text: "Canon City / Pueblo West / Westciffe / Penrose",
  },
  {
    key: "Craig OR Hamilton OR Steamboat Springs OR Maybell OR Hayden OR Meeker OR Clark OR Oak Creek OR Phippsburg OR Dinosaur",
    value:
      "Craig OR Hamilton OR Steamboat Springs OR Maybell OR Hayden OR Meeker OR Clark OR Oak Creek OR Phippsburg OR Dinosaur",
    text: "Craig",
  },
  {
    key: "Durango OR Mancos OR Hesperus OR Bayfield OR Ignacio OR Arboles OR Cortez",
    value:
      "Durango OR Mancos OR Hesperus OR Bayfield OR Ignacio OR Arboles OR Cortez",
    text: "Durango",
  },
  {
    key: "Fort Collins OR Windsor OR Wellington OR Greeley OR Bellvue OR Loveland OR Berthoud OR Ault OR Timnath OR Severance OR Eaton OR Evans OR Johnstown OR Longmont OR Pierce OR Nunn OR Kersey OR Drake OR Livermore OR Red Feature Lakes OR Carr OR Mead OR Platteville OR La Salle OR Firestone OR Frederick OR Miliken",
    value:
      "Fort Collins OR Windsor OR Wellington OR Greeley OR Bellvue OR Loveland OR Berthoud OR Ault OR Timnath OR Severance OR Eaton OR Evans OR Johnstown OR Longmont OR Pierce OR Nunn OR Kersey OR Drake OR Livermore OR Red Feature Lakes OR Carr OR Mead OR Platteville OR La Salle OR Firestone OR Frederick OR Miliken",
    text: "Fort Collins / Windsor / Wellington / Greeley",
  },
  {
    key: "Grand Junction OR Fruita OR Palisade OR Mack OR Loma OR Glade Park OR Mesa OR Molina OR Clifton OR Whitewater OR Parachute",
    value:
      "Grand Junction OR Fruita OR Palisade OR Mack OR Loma OR Glade Park OR Mesa OR Molina OR Clifton OR Whitewater OR Parachute",
    text: "Grand Junction / Fruita",
  },
  {
    key: "Gunnison OR Parlin OR Almont OR Crested Butte OR Pitkin OR Ohio City OR Powderhorn OR Sargents OR Lake City OR Whitepine OR Montrose OR Paonia OR Hotchkiss OR Buena Vista OR Salida OR Tincup",
    value:
      "Gunnison OR Parlin OR Almont OR Crested Butte OR Pitkin OR Ohio City OR Powderhorn OR Sargents OR Lake City OR Whitepine OR Montrose OR Paonia OR Hotchkiss OR Buena Vista OR Salida OR Tincup",
    text: "Gunnison / Crested Butte",
  },
  {
    key: "Fort Collins OR Windsor OR Wellington OR Greeley OR Bellvue OR Loveland OR Berthoud OR Ault OR Timnath OR Severance OR Eaton OR Evans OR Johnstown OR Longmont OR Pierce OR Nunn OR Kersey OR Drake OR Livermore OR Carr OR Mead OR Platteville OR La Salle OR Firestone OR Frederick OR Miliken OR Gilcrest OR Fort Lupton OR Brighton OR Erie",
    value:
      "Fort Collins OR Windsor OR Wellington OR Greeley OR Bellvue OR Loveland OR Berthoud OR Ault OR Timnath OR Severance OR Eaton OR Evans OR Johnstown OR Longmont OR Pierce OR Nunn OR Kersey OR Drake OR Livermore OR Carr OR Mead OR Platteville OR La Salle OR Firestone OR Frederick OR Miliken OR Gilcrest OR Fort Lupton OR Brighton OR Erie",
    text: "Johnstown / Loveland",
  },
  {
    key: "Monte Vista OR Del Norte OR South Fork OR Creede OR Center OR Hooper OR Mosca OR Alamosa OR Capulin OR La Jara OR Fort Garland OR Jasper OR Antonito OR Sanford OR Blanca OR Saguache OR Moffat OR Mosca OR Fort Garland OR Bonanza OR Crestone OR Poncha Springs OR Pagosa Springs OR San Luis OR La Veta OR Gardner",
    value:
      "Monte Vista OR Del Norte OR South Fork OR Creede OR Center OR Hooper OR Mosca OR Alamosa OR Capulin OR La Jara OR Fort Garland OR Jasper OR Antonito OR Sanford OR Blanca OR Saguache OR Moffat OR Mosca OR Fort Garland OR Bonanza OR Crestone OR Poncha Springs OR Pagosa Springs OR San Luis OR La Veta OR Gardner",
    text: "Monte Vista / Del Norte / Alamosa",
  },
  {
    key: "Montrose OR Durango OR Ridgway OR Cortez OR Cedaredge OR Olathe OR Austin OR Delta OR Crawford OR Cimarron OR Eckert OR Cedaredge OR Powderhorn OR Placerville OR Ouray",
    value:
      "Montrose OR Durango OR Ridgway OR Cortez OR Cedaredge OR Olathe OR Austin OR Delta OR Crawford OR Cimarron OR Eckert OR Cedaredge OR Powderhorn OR Ouray",
    text: "Montrose",
  },
  {
    key: "Paonia OR Crawford OR Hotchkiss OR Delta OR Cedaredge OR Somerset",
    value: "Paonia OR Crawford OR Hotchkiss OR Delta OR Cedaredge OR Somerset",
    text: "Paonia",
  },
  {
    key: "Ridgway OR Loghill Village OR Eldredge OR Vernal OR Montrose OR Cimarron OR Ouray OR Telluride OR Ophir OR Silverton OR Eureka OR Sawpit OR Placerville OR Dunton OR Pagosa Springs OR Lake City OR Cortez OR Durango OR Bayfield OR Ignacio",
    value:
      "Ridgway OR Loghill Village OR Eldredge OR Vernal OR Montrose OR Cimarron OR Ouray OR Telluride OR Ophir OR Silverton OR Eureka OR Sawpit OR Placerville OR Dunton OR Pagosa Springs OR Lake City OR Cortez OR Durango OR Bayfield OR Ignacio",
    text: "Ridgway / Ouray / Silverton",
  },
  {
    key: "Pagosa Springs OR Chromo OR Arboles OR Bayfield OR Durango OR Homelake OR Ignacio OR Monte Vista OR South Fork",
    value:
      "Pagosa Springs OR Chromo OR Arboles OR Bayfield OR Durango OR Homelake OR Ignacio OR Monte Vista OR South Fork",
    text: "Pagosa Springs",
  },

  {
    key: "South Fork OR Alpine OR Gerrard OR Creede OR Del Norte OR Monte Vista OR Center OR Jasper OR Platoro OR Alamosa",
    value:
      "South Fork OR Alpine OR Gerrard OR Creede OR Del Norte OR Monte Vista OR Center OR Jasper OR Platoro OR Alamosa",
    text: "South Fork",
  },
  {
    key: "Steamboat Springs OR Hayden OR Clark OR Oak Creek OR Phippsburg OR Kremmling OR Yampa OR Craig OR Coalmont OR Toponas",
    value:
      "Steamboat Springs OR Hayden OR Clark OR Oak Creek OR Phippsburg OR Kremmling OR Yampa OR Craig OR Coalmont OR Toponas",
    text: "Steamboat",
  },
  {
    key: "Walsenburg OR Gardner OR Rye OR La Veta OR Aguilar OR Trinidad OR Cuchara OR Fort Garland OR Model OR Colorado City OR Gardner",
    value:
      "Walsenburg OR Gardner OR Rye OR La Veta OR Aguilar OR Trinidad OR Cuchara OR Fort Garland OR Model OR Colorado City OR Gardner",
    text: "Walsenburg",
  },
  {
    key: "Cortez OR Slick Rock OR Dove Creek OR Cahone OR Pleasant View OR Dolores OR Mancos OR Hesperus OR Durango OR Breen OR Bayfield OR Rico OR Dunton OR Ophir OR Telluride OR Naturita OR Redvale OR Norwood OR Nucla OR Bedrock OR Uravan",
    value:
      "Cortez OR Slick Rock OR Dove Creek OR Cahone OR Pleasant View OR Dolores OR Mancos OR Hesperus OR Durango OR Breen OR Bayfield OR Rico OR Dunton OR Ophir OR Telluride OR Naturita OR Redvale OR Norwood OR Nucla OR Bedrock OR Uravan",
    text: "Cortez",
  },
];

const mlsAccessOptions = [
  { key: "CO_CREN", value: "CO_CREN", text: "CREN" },
  { key: "CO_IRES", value: "CO_IRES", text: "IRES" },
  { key: "CO_RECOLORADO", value: "CO_RECOLORADO", text: "RECOLORADO" },
  { key: "CO_PPMLS", value: "CO_PPMLS", text: "PPMLS" },
  { key: "CO_SUMMIT", value: "CO_SUMMIT", text: "SUMMIT" },
  { key: "CO_Pueblo", value: "CO_Pueblo", text: "PUEBLO" },
  { key: "CO_VMLS", value: "CO_VMLS", text: "VAIL" },
  {
    key: "CO_ASPENGLENWOOD",
    value: "CO_ASPENGLENWOOD",
    text: "ASPEN / GLENWOOD",
  },
  { key: "CO_GRANDCOUNTY", value: "CO_GRANDCOUNTY", text: "GRAND COUNTY" },
  {
    key: "CO_GRANDJUNCTION",
    value: "CO_GRANDJUNCTION",
    text: "GRAND JUNCTION",
  },
  { key: "CO_TELLURIDE", value: "CO_TELLURIDE", text: "TELLURIDE" },
  { key: "CO_ROYALGORGE", value: "CO_ROYALGORGE", text: "ROYAL GORGE" },
  { key: "CO_SPANISHPEAKS", value: "CO_SPANISHPEAKS", text: "SPANISH PEAKS" },
];

const userBrokerageLogoOptions = [
  { key: "true", text: "true", value: "true" },
  { key: "false", text: "false", value: "false" },
];

const freeTrialDurationOptions = [
  { key: "1", value: 1, text: "1 Month" },
  { key: "2", value: 2, text: "2 Months" },
  { key: "3", value: 3, text: "3 Months" },
  { key: "6", value: 6, text: "6 Months" },
  { key: "12", value: 12, text: "12 Months" },
  { key: "24", value: 24, text: "2 Years" },
  { key: "36", value: 36, text: "3 Years" },
  { key: "48", value: 48, text: "4 Years" },
  { key: "60", value: 60, text: "5 Years" },
  { key: "endOfYear", value: "endOfYear", text: "Until End of Year" },
];

const additionalFormsAccessOptions = [
  { key: "LEGAL_COX", value: "LEGAL_COX", text: "Cox Legal Forms" },
  {
    key: "LEGAL_FRASCONA",
    value: "LEGAL_FRASCONA",
    text: "Frascona Legal Forms",
  },
  { key: "MLS_CREN", value: "MLS_CREN", text: "CREN MLS delayed Entry Form" },
  { key: "MLS_IRES", value: "MLS_IRES", text: "IRES MLS delayed Entry Form" },
];

// Component to display logo preview as hover popup
function LogoPreview() {
  const { values } = useFormikContext();

  if (!values.brokerageLogoRef) {
    return null;
  }

  return (
    <div style={{ marginTop: "10px", display: "inline-block" }}>
      <Popup
        trigger={
          <span
            style={{
              color: "#2185d0",
              cursor: "pointer",
              textDecoration: "underline",
              fontWeight: "bold",
            }}
          >
            Logo Preview
          </span>
        }
        content={
          <img
            src={`https://storage.googleapis.com/transact-staging.appspot.com/brokerageLogos/${values.brokerageLogoRef}/logo.png`}
            alt="Brokerage Logo"
            style={{
              maxHeight: "100px",
              border: "1px solid #ccc",
              borderRadius: "4px",
            }}
            onError={(e) => {
              e.target.style.display = "none";
            }}
          />
        }
        position="right center"
        on="hover"
      />
    </div>
  );
}

// Component to display logo preview in table cell
function LogoPreviewCell({ brokerageLogoRef, isFullUrl = false }) {
  if (!brokerageLogoRef) {
    return <span>No Logo</span>;
  }

  // Determine the image URL based on whether it's a full URL or partial reference
  const imageUrl = isFullUrl
    ? brokerageLogoRef
    : `https://storage.googleapis.com/transact-staging.appspot.com/brokerageLogos/${brokerageLogoRef}/logo.png`;

  return (
    <Popup
      trigger={
        <span
          style={{
            color: "#2185d0",
            cursor: "pointer",
            textDecoration: "underline",
            fontWeight: "bold",
          }}
        >
          Preview Logo
        </span>
      }
      content={
        <img
          src={imageUrl}
          alt="Brokerage Logo"
          style={{
            maxHeight: "100px",
            border: "1px solid #ccc",
            borderRadius: "4px",
          }}
          onError={(e) => {
            e.target.style.display = "none";
          }}
        />
      }
      position="right center"
      on="hover"
    />
  );
}

export default function AdminPage() {
  const { forms } = useSelector((state) => state.doc);
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [authLog, setAuthLog] = useState([]);
  const [usersWithCustomClaims, setUsersWithCustomClaims] = useState({});
  const [formFieldConversionArray, setFormFieldConversionArray] = useState([]);
  const [userLookupResults, setUserLookupResults] = useState([]);
  const [userLookupSearchTerms, setUserLookupSearchTerms] = useState("");
  const [isSearchingUsers, setIsSearchingUsers] = useState(false);
  const [managerOptions, setManagerOptions] = useState([]);
  const [expireFormMessage, setExpireFormMessage] = useState("");
  const [editingUser, setEditingUser] = useState(null);
  const [showEditUserModal, setShowEditUserModal] = useState(false);
  const [expireFormMessageType, setExpireFormMessageType] = useState(""); // "success" or "error"
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  const dispatch = useDispatch();

  // Fetch managers on component mount
  React.useEffect(() => {
    const loadManagers = async () => {
      try {
        const managersData = await fetchAllManagersFromDb();
        const options = [
          { key: "", value: "", text: "No Manager" },
          ...managersData.map((manager) => ({
            key: manager.id,
            value: manager.id,
            text: `${manager.firstName || ""} ${manager.lastName || ""} (${
              manager.email || ""
            })`,
          })),
        ];
        setManagerOptions(options);
      } catch (error) {
        console.error("Error fetching managers:", error);
        toast.error("Error loading managers");
      }
    };
    loadManagers();
  }, []);

  function logResult(result) {
    console.log(" AUTH::result = ", result?.result);
    setAuthLog([...authLog, result?.result]);
    toast.success(result?.result);
  }
  function logError(error) {
    console.log("AUTH:: ERROR: ", error);
    toast.error(error.message);
  }

  const handleExpireForm = async (selectedState, selectedFormId) => {
    try {
      // Get the specific form by ID from the state forms collection
      const formRef = doc(db, `forms${selectedState}`, selectedFormId);
      const formDoc = await getDoc(formRef);

      if (!formDoc.exists()) {
        throw new Error("Form not found");
      }

      const formData = formDoc.data();
      const formTitle = formData.title;

      const batch = writeBatch(db);

      // Update the form to set dontShowFormInAddDocumentModal = true
      batch.update(formRef, {
        dontShowFormInAddDocumentModal: true,
      });

      let templatesCount = 0;

      // Only update form templates if the form can be a template
      if (formData.canBeTemplate === true) {
        const formTemplatesRef = collection(db, "formTemplates");
        const templatesQuery = query(
          formTemplatesRef,
          where("title", "==", formTitle)
        );
        const templatesSnapshot = await getDocs(templatesQuery);

        console.log(
          "# form templates setting to expired = ",
          templatesSnapshot.docs.length
        );

        templatesCount = templatesSnapshot.docs.length;

        if (templatesSnapshot.docs.length > 0) {
          templatesSnapshot.docs.forEach((templateDoc) => {
            const templateRef = doc(db, "formTemplates", templateDoc.id);
            batch.update(templateRef, {
              dontShowFormInAddDocumentModal: true,
            });
          });
        }
      }

      await batch.commit();

      console.log(
        `Successfully expired form "${formTitle}" and ${templatesCount} matching templates`
      );

      const message =
        formData.canBeTemplate === true
          ? `Successfully expired form "${formTitle}" and ${templatesCount} matching form templates.`
          : `Successfully expired form "${formTitle}". No form templates were updated (form cannot be a template).`;

      return { success: true, message };
    } catch (error) {
      console.error("Error expiring form:", error);
      const errorMessage = "Error expiring form. Please try again.";
      return { success: false, message: errorMessage };
    }
  };

  async function handleViewUserRole(data) {
    try {
      const result = await functionViewAuthClaimRole(data);
      logResult(result);
    } catch (error) {
      logError(error);
    }
  }

  async function handleAuthRole(data) {
    try {
      const result = await functionAddAuthClaimRole(data);
      logResult(result);
    } catch (error) {
      logError(error);
    }
  }

  async function handleAuthAssistant(data) {
    try {
      const result = await functionAddAuthClaimAssistant(data);
      logResult(result);
    } catch (error) {
      logError(error);
    }

    try {
      console.log("agentAssistingEmail = ", data?.agentAssistingEmail);
      console.log("data.assistantEmail = ", data?.email);
      if (data?.agentAssistingEmail && data?.email) {
        const agent = await getUserProfileByEmailFromDb(
          data?.agentAssistingEmail
        );
        const assistantProf = await getUserProfileByEmailFromDb(data?.email);
        console.log("Assistant Profile: ", assistantProf);
        if (agent && agent[0] && agent[0].id && assistantProf) {
          await updateProfileFieldsInDb(agent[0]?.id, {
            assistantEmail: data.email,
            assistant: assistantProf,
            hasAssistant: true,
          });
        }
      }
    } catch (error) {
      console.log("ERROR updating assistant: ", error);
    }
  }

  async function handleAuthAssistDelete(data) {
    try {
      const result = await functionDelAuthClaimAssistant(data);
      logResult(result);
    } catch (error) {
      logError(error);
    }
  }

  // Add these new handler functions
  async function handleAuthManagerAssistant(data) {
    try {
      const result = await functionAddAuthClaimManagerAssistant(data);
      logResult(result);
    } catch (error) {
      logError(error);
    }

    try {
      console.log("managerEmail = ", data?.managerEmail);
      console.log("data.email = ", data?.email);
      if (data?.managerEmail && data?.email) {
        const manager = await getUserProfileByEmailFromDb(data?.managerEmail);
        const assistantProf = await getUserProfileByEmailFromDb(data?.email);
        console.log("Manager Assistant Profile: ", assistantProf);
        if (manager && manager[0] && manager[0].id && assistantProf?.[0]) {
          const managerAssistantInfo = {
            userId: assistantProf[0].id || assistantProf[0].userId,
            email: assistantProf[0].email,
            firstName: assistantProf[0].firstName || "",
            lastName: assistantProf[0].lastName || "",
            title: data.title || "Manager Assistant",
          };

          // Update manager profile
          let managerAssistants = manager[0].managerAssistants || [];

          console.log("here we are managerAssistants = ", managerAssistants);
          console.log("the managerAssistantInfo = ", managerAssistantInfo);

          // Check if this assistant is already in the list
          const existingIndex = managerAssistants?.findIndex(
            (a) =>
              a.userId === assistantProf.id || a.userId === assistantProf.userId
          );
          console.log("Existing Index: ", existingIndex || "broken");
          if (existingIndex >= 0) {
            // Update existing assistant info
            managerAssistants[existingIndex] = managerAssistantInfo;
          } else {
            // Add new assistant
            managerAssistants.push(managerAssistantInfo);
          }
          console.log(
            "AFTER push or add managerAssistants = ",
            managerAssistants
          );

          await updateProfileFieldsInDb(manager[0]?.id, {
            hasManagerAssistants: true,
            managerAssistants: managerAssistants,
          });

          await updateProfileFieldsInDb(assistantProf[0]?.id, {
            roleTitle: data.title || "Manager Assistant",
          });

          console.log("data.title = ", data.title);

          // Now update all agents that have this managerId
          // First, get all agents with this managerId
          const agentsQuery = query(
            collection(db, "users"),
            where("managerId", "==", manager[0].id)
          );

          const agentsSnapshot = await getDocs(agentsQuery);

          // Update each agent's profile with the manager assistant info
          const batch = writeBatch(db);

          agentsSnapshot.docs.forEach((doc) => {
            console.log("doc...who =", doc.data()?.lastName);
            // const agentData = doc.data();
            // let agentManagerAssistants = agentData?.managerAssistants || [];

            // // Check if this assistant is already in the list
            // const agentExistingIndex = agentManagerAssistants?.findIndex(
            //   (a) =>
            //     a.userId === assistantProf.id ||
            //     a.userId === assistantProf.userId
            // );
            // if (agentExistingIndex >= 0) {
            //   // Update existing assistant info
            //   agentManagerAssistants[agentExistingIndex] = managerAssistantInfo;
            // } else {
            //   // Add new assistant
            //   agentManagerAssistants.push(managerAssistantInfo);
            // }

            batch.update(doc.ref, {
              managerAssistants: managerAssistants || [],
              hasManagerAssistants: true,
            });
          });

          await batch.commit();
          console.log(
            `Updated ${agentsSnapshot.size} agent profiles with manager assistant info`
          );
        }
      }
    } catch (error) {
      console.log("ERROR updating manager assistant: ", error);
    }
  }

  async function handleAuthManagerAssistDelete(data) {
    try {
      const result = await functionDelAuthClaimManagerAssistant(data);
      logResult(result);
    } catch (error) {
      logError(error);
    }

    try {
      console.log("managerEmail = ", data?.managerEmail);
      console.log("data.email = ", data?.email);
      if (data?.managerEmail && data?.email) {
        // Get the manager profile
        const manager = await getUserProfileByEmailFromDb(data?.managerEmail);
        const assistantProf = await getUserProfileByEmailFromDb(data?.email);

        if (manager && manager[0] && manager[0].id && assistantProf) {
          // Update manager profile
          let managerAssistants = manager[0].managerAssistants || [];

          // Remove this assistant from the list
          managerAssistants = managerAssistants.filter(
            (a) =>
              a.userId !== assistantProf.id && a.userId !== assistantProf.userId
          );

          await updateProfileFieldsInDb(manager[0]?.id, {
            managerAssistants: managerAssistants,
            hasManagerAssistants: managerAssistants.length > 0,
            // Only remove these if this was the only manager assistant
            ...(managerAssistants.length === 0
              ? {
                  managerAssistants: [],
                  hasManagerAssistants: false,
                }
              : {}),
          });

          // Now update all agents that have this managerId
          // First, get all agents with this managerId
          const agentsQuery = query(
            collection(db, "users"),
            where("managerId", "==", manager[0].id)
          );

          const agentsSnapshot = await getDocs(agentsQuery);

          // Update each agent's profile to remove the manager assistant info
          const batch = writeBatch(db);

          agentsSnapshot.docs.forEach((doc) => {
            const agentData = doc.data();
            let agentManagerAssistants = agentData.managerAssistants || [];

            // Remove this assistant from the list
            agentManagerAssistants = agentManagerAssistants.filter(
              (a) =>
                a.userId !== assistantProf.id &&
                a.userId !== assistantProf.userId
            );

            batch.update(doc.ref, {
              managerAssistants: agentManagerAssistants,
              hasManagerAssistants: agentManagerAssistants.length > 0,
            });
          });

          await batch.commit();
          console.log(
            `Updated ${agentsSnapshot.size} agent profiles to remove manager assistant info`
          );
        }
      }
    } catch (error) {
      logError(error);
    }
  }

  async function handleGetDataFinitiViews() {
    try {
      const result = await functionFetchViewsFromDataFiniti();
      console.log("DATAFINITI VIEWS: ", result);
    } catch (error) {
      console.log("ERROR: ", error);
    }
  }

  async function handleCreateDataFinitiView() {
    try {
      const result = await functionCreateViewInDataFiniti();
      console.log("DATAFINITI VIEWS: ", result);
    } catch (error) {
      console.log("ERROR: ", error);
    }
  }

  async function handleViewAllCustomClaims() {
    try {
      let usersByRole = { tcs: [], assistants: [], managers: [], admins: [] };
      const result = await functionViewAllUsersWithCustomClaims();
      result.forEach((user) => {
        if (user.authCustomClaims?.r === "t") {
          usersByRole.tcs.push(user);
        } else if (user.authCustomClaims?.r === "a") {
          usersByRole.assistants.push(user);
        } else if (user.authCustomClaims?.r === "m") {
          usersByRole.managers.push(user);
        } else if (user.authCustomClaims?.r === "d") {
          usersByRole.admins.push(user);
        }
      });
      console.log("USER BY ROLE: ", usersByRole);
      setUsersWithCustomClaims(usersByRole);
    } catch (error) {
      logError(error);
    }
  }

  async function handleUpdateUserEmail(data) {
    try {
      console.log("UPDATE USER EMAIL DATA: ", data);
      const result = await functionUpdateUserEmail(data);
      console.log("TOKEN: ", result);
      //   signInCustomAuth(result)
      //     .then((userCredential) => {
      //       console.log("SIGNED IN AS: ", userCredential.user);
      //       window.location.reload(false);
      //     })
      //     .catch((error) => {
      //       logError(error);
      //     });
    } catch (error) {
      logError(error);
    }
  }

  async function handleImpersonate(data) {
    // testing requires emulator=false
    try {
      // console.log("IMPERSONATE DATA: ", data);

      const result = await functionImpersonateUser(data);
      // console.log("TOKEN: ", result);
      signInCustomAuth(result)
        .then(async (userCredential) => {
          // console.log("SIGNED IN AS: ", userCredential.user);

          // Get the user profile to determine routing
          const profile = await getUserProfileFromDb(userCredential.user.uid);
          // console.log("USER PROFILE: ", profile);

          // Apply routing logic based on user role and type
          let redirectPath = "/overview"; // default

          if (profile.role === "tc") {
            redirectPath = "/overviewTc";
          } else if (profile.type === "party") {
            redirectPath = "/overviewParty";
          } else if (profile.role === "manager" && profile.type === "user") {
            redirectPath = "/calendarManager";
          } else if (profile.role === "agent" && profile.type === "user") {
            redirectPath = "/overview";
          } else if (
            profile.role === "managerassistant" &&
            profile.type === "user"
          ) {
            redirectPath = "/calendarManager";
          } else if (profile.role === "coagent" && profile.type === "user") {
            redirectPath = "/overview";
          } else if (profile.role === "assistant" && profile.type === "user") {
            redirectPath = "/overview";
          }

          // console.log("REDIRECTING TO: ", redirectPath);
          // Use window.location.href to ensure proper navigation after impersonation
          // The key change I made is using window.location.href instead of navigate(). This is important because:
          // 1. Authentication State: After impersonation, the authentication state needs to be fully established before React Router can properly handle navigation
          // 2. Full Page Reload: Using window.location.href forces a complete page reload, which ensures that all authentication state, Redux store, and routing are properly initialized for the impersonated user
          // 3. Timing Issues: The navigate() function from React Router might execute before the authentication context is fully updated
          window.location.href = redirectPath;
        })
        .catch((error) => {
          logError(error);
        });
    } catch (error) {
      logError(error);
    }
  }

  async function handleViewFormFieldConversions() {
    let fieldConversionArray = [];
    forms &&
      forms.forEach(async (form) => {
        // if (form.title !== "Agreement to Revive") {
        //   return;
        // }
        console.log("GETTING FORMID: ", form.id);
        const fieldConversions = await getFormFieldConversionsFromDb(
          form.id,
          currentUserProfile
        );
        console.log("FIELDCONVERSIONS", fieldConversions);
        fieldConversionArray = [...fieldConversionArray, { title: form.title }];
        fieldConversions.forEach((fieldConversion) => {
          fieldConversionArray = [...fieldConversionArray, fieldConversion];
        });
        console.log("FIELDARRAY BEFORE: ", fieldConversionArray);
        setFormFieldConversionArray(fieldConversionArray);
      });
  }

  async function handleUpdateManagerDetails(data) {
    try {
      if (!data?.managerEmail) {
        toast.error("Manager email is required");
        return;
      }

      // Get the manager profile
      const manager = await getUserProfileByEmailFromDb(data?.managerEmail);

      if (manager && manager[0] && manager[0].id) {
        // Create manager details object
        const managerDetails = {
          firstName: manager[0].firstName || "",
          lastName: manager[0].lastName || "",
          managerEmail: data.managerEmail,
          sendEmailNotificationsOfNewTransactions:
            data.sendEmailNotifications || false,
        };

        // Update manager profile with the new details
        await updateProfileFieldsInDb(manager[0]?.id, {
          managerDetails: managerDetails,
        });

        // Now update all agents that have this managerId
        const agentsQuery = query(
          collection(db, "users"),
          where("managerId", "==", manager[0].id)
        );

        const agentsSnapshot = await getDocs(agentsQuery);

        // Update each agent's profile with the manager details
        const batch = writeBatch(db);

        agentsSnapshot.docs.forEach((doc) => {
          batch.update(doc.ref, {
            managerDetails: managerDetails,
          });
        });

        await batch.commit();
        logResult(
          `Updated ${agentsSnapshot.size} agent profiles with manager details`
        );
      } else {
        logError("Manager not found");
      }
    } catch (error) {
      logError(error);
    }
  }

  // Helper function to normalize phone numbers by removing formatting
  function normalizePhoneNumber(phone) {
    if (!phone) return "";
    return phone.replace(/[\s\-().]/g, "");
  }

  function handleEditUser(user) {
    setEditingUser(user);
    setShowEditUserModal(true);
  }

  function handleCloseEditModal() {
    setEditingUser(null);
    setShowEditUserModal(false);
  }

  async function handleUpdateUser(values) {
    try {
      // Process stateProvince as multi-select array
      const stateProvince =
        values.stateProvinceOptions
          ?.map((code) => {
            const option = stateProvinceOptions.find(
              (opt) => opt.stateProvinceCode === code
            );
            return option
              ? {
                  stateProvinceCode: option.stateProvinceCode,
                  stateProvinceName: option.displayName,
                }
              : null;
          })
          .filter(Boolean) || [];

      // Process MLS access similar to Create New User
      const mlsAccess =
        values?.mlsAccessOptions
          ?.map((key) => {
            const match = mlsAccessOptions.find((opt) => opt.key === key);
            return match ? { mlsIdCode: key, mlsName: match.text } : null;
          })
          .filter(Boolean) || [];

      // Convert brokerageLogoRef to full URL if it's not empty
      const fullLogoUrl = values.brokerageLogoRef
        ? `https://storage.googleapis.com/transact-staging.appspot.com/brokerageLogos/${values.brokerageLogoRef}/logo.png`
        : "";

      // Create the update payload
      const updateData = {
        ...values,
        stateProvince,
        mlsAccess,
        brokerageLogoRef: fullLogoUrl,
      };

      // Remove fields that shouldn't be updated
      delete updateData.stateProvinceOptions;
      delete updateData.mlsAccessOptions;
      delete updateData.latestPayment;

      // Call Firebase function to update user
      await functionUpdateUser(updateData);

      toast.success("User successfully updated");
      handleCloseEditModal();

      // Refresh user lookup results
      if (userLookupSearchTerms) {
        handleUserLookup(userLookupSearchTerms);
      }
    } catch (error) {
      toast.error(error.message);
    }
  }

  async function handleUserLookup(searchTerms) {
    if (!searchTerms || searchTerms.trim().length < 2) {
      setUserLookupResults([]);
      return;
    }

    setIsSearchingUsers(true);
    try {
      const usersRef = collection(db, "users");
      const searchTerm = searchTerms.trim().toLowerCase();
      const normalizedSearchTerm = normalizePhoneNumber(searchTerm);

      // Create multiple queries for different search criteria
      const queries = [];

      // Search by email
      if (searchTerm.includes("@")) {
        queries.push(query(usersRef, where("email", "==", searchTerm)));
      }

      // For phone and name searches, we'll get all users and filter client-side
      // since Firestore doesn't support case-insensitive partial text search
      // and we need to normalize phone numbers
      queries.push(query(usersRef));

      const allResults = [];

      for (const q of queries) {
        const snapshot = await getDocs(q);
        snapshot.docs.forEach((doc) => {
          const userData = { id: doc.id, ...doc.data() };
          // Avoid duplicates
          if (!allResults.find((user) => user.id === userData.id)) {
            allResults.push(userData);
          }
        });
      }

      // Filter results based on search terms (client-side filtering)
      const filteredResults = allResults.filter((user) => {
        const firstName = (user.firstName || "").toLowerCase();
        const middleName = (user.middleName || "").toLowerCase();
        const lastName = (user.lastName || "").toLowerCase();
        const email = (user.email || "").toLowerCase();
        const phone = (user.phone || "").toLowerCase();
        const normalizedUserPhone = normalizePhoneNumber(user.phone || "");
        const brokerageName = (user.brokerageName || "").toLowerCase();

        // Check if search term contains digits (potential phone search)
        const isPhoneSearch = /\d/.test(searchTerm);

        return (
          firstName.includes(searchTerm) ||
          middleName.includes(searchTerm) ||
          lastName.includes(searchTerm) ||
          email.includes(searchTerm) ||
          phone.includes(searchTerm) ||
          brokerageName.includes(searchTerm) ||
          `${firstName} ${lastName}`.includes(searchTerm) ||
          `${firstName} ${middleName} ${lastName}`.includes(searchTerm) ||
          // Phone number matching with normalized numbers
          (isPhoneSearch && normalizedUserPhone.includes(normalizedSearchTerm))
        );
      });

      setUserLookupResults(filteredResults.slice(0, 50)); // Limit to 50 results
    } catch (error) {
      console.error("Error searching users:", error);
      toast.error("Error searching users");
      setUserLookupResults([]);
    } finally {
      setIsSearchingUsers(false);
    }
  }

  return (
    <div className="main-page-wrapper">
      <Segment clearing>
        <Formik
          initialValues={{
            email: "",
          }}
          validationSchema={Yup.object({
            email: Yup.string().required().email(),
          })}
          onSubmit={(values, { setSubmitting, setFieldValue }) => {
            handleImpersonate(values);
            setSubmitting(false);
          }}
        >
          {({
            values,
            handleChange,
            handleSubmit,
            isSubmitting,
            isValid,
            dirty,
            errors,
          }) => (
            <Form className="ui form medium margin bottom">
              <Header size="huge" color="blue">
                Impersonate User
              </Header>
              <Divider />
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <Grid.Column width={4}>
                      <MyTextInput
                        name="email"
                        type="email"
                        label="Email"
                        placeholder="Email address"
                      />
                      {errors.auth && (
                        <Label
                          basic
                          color="red"
                          style={{ marginBottom: 10 }}
                          content={errors.auth}
                        />
                      )}
                    </Grid.Column>

                    <Grid.Column>
                      <br />
                      <Button
                        name="button"
                        value={FormAction.authSet}
                        primary
                        type="button"
                        onChange={handleChange}
                        loading={isSubmitting}
                        disabled={!isValid || !dirty || isSubmitting}
                        onClick={() => {
                          handleSubmit();
                        }}
                        size="medium"
                        style={{ marginBottom: 10 }}
                        content="Impersonate User"
                      />
                    </Grid.Column>
                  </Grid.Column>
                </Grid.Row>
              </Grid>
            </Form>
          )}
        </Formik>
      </Segment>
      <Segment clearing>
        <div className="ui form medium margin bottom">
          <Header size="huge" color="blue">
            User Lookup
          </Header>
          <p className="bold text-medium">
            Search for users by phone number (any format - ignores spaces,
            dashes, parentheses), email, first and/or last name.
          </p>
          <Divider />
          <Grid>
            <Grid.Row>
              <Grid.Column mobile={16} computer={8}>
                <Input
                  type="text"
                  fluid
                  placeholder="Search by phone (any format), email, first name, last name, or brokerage name"
                  value={userLookupSearchTerms}
                  onChange={(e) => {
                    const value = e.target.value;
                    setUserLookupSearchTerms(value);
                    handleUserLookup(value);
                  }}
                  loading={isSearchingUsers}
                />
              </Grid.Column>
            </Grid.Row>
            {userLookupResults.length > 0 && (
              <Grid.Row>
                <Grid.Column mobile={16} computer={16}>
                  <Header size="medium" color="blue">
                    Search Results ({userLookupResults.length})
                  </Header>
                  <div
                    style={{
                      maxHeight: "400px",
                      overflowY: "auto",
                      overflowX: "auto",
                    }}
                  >
                    <Table celled striped style={{ minWidth: "1800px" }}>
                      <Table.Header>
                        <Table.Row>
                          <Table.HeaderCell>Edit</Table.HeaderCell>
                          <Table.HeaderCell>First Name</Table.HeaderCell>
                          <Table.HeaderCell>Middle Name</Table.HeaderCell>
                          <Table.HeaderCell>Last Name</Table.HeaderCell>
                          <Table.HeaderCell width={2}>Phone</Table.HeaderCell>
                          <Table.HeaderCell>Email</Table.HeaderCell>
                          <Table.HeaderCell>Manager Email</Table.HeaderCell>
                          <Table.HeaderCell>Brokerage Name</Table.HeaderCell>
                          <Table.HeaderCell>Brokerage Forms</Table.HeaderCell>
                          <Table.HeaderCell>Logo</Table.HeaderCell>
                          <Table.HeaderCell>Role</Table.HeaderCell>
                          <Table.HeaderCell>
                            Special Role Set Up?
                          </Table.HeaderCell>
                          <Table.HeaderCell>Account Expires</Table.HeaderCell>
                          <Table.HeaderCell>Active Account</Table.HeaderCell>
                          <Table.HeaderCell>Last Sign In</Table.HeaderCell>
                          <Table.HeaderCell>MLS Access</Table.HeaderCell>
                          <Table.HeaderCell width={3}>
                            Additional Forms Access
                          </Table.HeaderCell>
                          <Table.HeaderCell width={3}>
                            Referral
                          </Table.HeaderCell>
                          <Table.HeaderCell width={3}>Notes</Table.HeaderCell>
                          <Table.HeaderCell width={3}>Cities</Table.HeaderCell>
                          <Table.HeaderCell>Created At</Table.HeaderCell>
                        </Table.Row>
                      </Table.Header>
                      <Table.Body>
                        {userLookupResults.map((user) => {
                          // Calculate latest expiration date from payments
                          const getLatestExpiration = (payments) => {
                            if (!payments || payments.length === 0) return "";
                            const latestPayment = payments.reduce(
                              (latest, payment) => {
                                if (!payment.dateExpires) return latest;
                                const paymentDate = payment.dateExpires.toDate
                                  ? payment.dateExpires.toDate()
                                  : new Date(payment.dateExpires);
                                const latestDate = latest.dateExpires
                                  ? latest.dateExpires.toDate
                                    ? latest.dateExpires.toDate()
                                    : new Date(latest.dateExpires)
                                  : new Date(0);
                                return paymentDate > latestDate
                                  ? payment
                                  : latest;
                              },
                              {}
                            );
                            if (!latestPayment.dateExpires) return "";
                            const expDate = latestPayment.dateExpires.toDate
                              ? latestPayment.dateExpires.toDate()
                              : new Date(latestPayment.dateExpires);
                            return expDate.toLocaleDateString();
                          };

                          return (
                            <Table.Row key={user.id}>
                              <Table.Cell>
                                <Button
                                  size="mini"
                                  primary
                                  icon="edit"
                                  onClick={() => handleEditUser(user)}
                                />
                              </Table.Cell>
                              <Table.Cell>{user.firstName || ""}</Table.Cell>
                              <Table.Cell>{user.middleName || ""}</Table.Cell>
                              <Table.Cell>{user.lastName || ""}</Table.Cell>
                              <Table.Cell>{user.phone || ""}</Table.Cell>
                              <Table.Cell>{user.email || ""}</Table.Cell>
                              <Table.Cell>
                                {user.managerDetails?.managerEmail || ""}
                              </Table.Cell>
                              <Table.Cell>
                                {user.brokerageName || ""}
                              </Table.Cell>
                              <Table.Cell>
                                {user.brokerageForms || ""}
                              </Table.Cell>
                              <Table.Cell>
                                <LogoPreviewCell
                                  brokerageLogoRef={user.brokerageLogoRef}
                                  isFullUrl={true}
                                />
                              </Table.Cell>
                              <Table.Cell>{user.role || ""}</Table.Cell>
                              <Table.Cell>
                                {user.authCustomClaims?.r === "m"
                                  ? "Manager"
                                  : user.authCustomClaims?.r === "g"
                                  ? "Manager Assistant"
                                  : user.authCustomClaims?.r === "t"
                                  ? "TC"
                                  : user.authCustomClaims?.r === "a"
                                  ? "Assistant"
                                  : ""}
                              </Table.Cell>
                              <Table.Cell>
                                {getLatestExpiration(user.payments)}
                              </Table.Cell>
                              <Table.Cell>
                                {user.type === "user" ? "Yes" : "No"}
                              </Table.Cell>
                              <Table.Cell>
                                {user.lastSignIn?.toDate()
                                  ? user.lastSignIn
                                      .toDate()
                                      .toLocaleDateString()
                                  : ""}
                              </Table.Cell>
                              <Table.Cell>
                                {user.mlsAccess && user.mlsAccess.length > 0
                                  ? user.mlsAccess
                                      .map((mls) => mls.mlsName)
                                      .join(", ")
                                  : ""}
                              </Table.Cell>
                              <Table.Cell>
                                {user.additionalFormsAccess &&
                                user.additionalFormsAccess.length > 0
                                  ? user.additionalFormsAccess.join(", ")
                                  : ""}
                              </Table.Cell>
                              <Table.Cell>{user.referral || ""}</Table.Cell>
                              <Table.Cell>{user.notes || ""}</Table.Cell>
                              <Table.Cell style={{ minWidth: "280px" }}>
                                {user.cities || ""}
                              </Table.Cell>
                              <Table.Cell>
                                {user.createdAt?.toDate()
                                  ? user.createdAt.toDate().toLocaleDateString()
                                  : ""}
                              </Table.Cell>
                            </Table.Row>
                          );
                        })}
                      </Table.Body>
                    </Table>
                  </div>
                </Grid.Column>
              </Grid.Row>
            )}
            {userLookupSearchTerms &&
              userLookupResults.length === 0 &&
              !isSearchingUsers && (
                <Grid.Row>
                  <Grid.Column mobile={16} computer={16}>
                    <p>No users found matching your search criteria.</p>
                  </Grid.Column>
                </Grid.Row>
              )}
          </Grid>
        </div>
      </Segment>
      <Segment clearing>
        <Formik
          initialValues={{
            email: "",
            role: "agent",
            firstName: "",
            lastName: "",
            state: "Colorado",
            brokerageForms: "",
            hasBrokerageLogo: "true",
            phone: "",
            middleName: "",
            brokerageLogoRef: "",
            brokerageName: "",
            brokerageId: "",
            brokerageLicenseNumber: "",
            brokerLicenseNumber: "",
            brokerNrdsId: "",
            mlsAccessOptions: [],
            freeTrialDurationMonths: 1,
            additionalFormsAccess: [],
            managerId: "",
            notes: "",
            referral: "",
            address: {
              street: "",
              unit: "",
              city: "",
              state: "",
              zipcode: "",
            },
          }}
          validationSchema={Yup.object({
            email: Yup.string().required().email(),
            role: Yup.string().required(),
            firstName: Yup.string().required(),
            lastName: Yup.string().required(),
            state: Yup.string().required(),
            brokerageForms: Yup.string().required(),
          })}
          onSubmit={async (values, { setSubmitting, resetForm }) => {
            try {
              const mlsAccess = values?.mlsAccessOptions
                .map((key) => {
                  const match = mlsAccessOptions.find((opt) => opt.key === key);
                  return match ? { mlsIdCode: key, mlsName: match.text } : null;
                })
                .filter(Boolean); // remove nulls if any key wasn't found
              const valuesWithMLSaccess = {
                ...values,
                mlsAccess,
              };
              delete valuesWithMLSaccess.mlsAccessOptions;
              await functionCreateNewUser(valuesWithMLSaccess);
              resetForm();
              setSubmitting(false);
              toast.success("Profile successfully updated");
            } catch (error) {
              toast.error(error.message);
              setSubmitting(false);
            }
          }}
        >
          {({ isSubmitting, dirty, isValid, values }) => (
            <Form className="ui form medium margin bottom">
              <Header size="huge" color="blue">
                Create New User
              </Header>
              <p>
                <a
                  href="https://apps2.colorado.gov/dre/licensing/lookup/licenselookup.aspx"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Lookup Colorado License
                </a>
                <br /> <br />
                Copy-paste Brokerage Forms to Brokerage Logo Ref and verify by
                mouse-hover over "Logo Preview".
                <br />
                Usually this is the webURL of the brokerage website, eg., KW,
                COMPASS, etc. If it doesn't end with .com, then add DOTINFO or
                DOTWHATEVER. Hint: use the Lookup above to find what it might
                be.
                <br /> Exceptions: CB, HS (HomeSmart), BHHS (Berkshire), Galles
                <br /> <br />
                <i>
                  Note: For Resident Realty, they are good until Aug 31, 2030.
                  So just set it for 5 years out.
                </i>
              </p>
              <Header color="blue">Name & Contact Info</Header>
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput name="firstName" label="First name" />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput name="middleName" label="Middle name" />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput name="lastName" label="Last name" />
                  </Grid.Column>
                </Grid.Row>
                <Grid.Row className="zero top padding">
                  <Grid.Column mobile={16} computer={5}>
                    <MyEmailInput name="email" label="Email" />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput name="phone" label="Phone" />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={5}>
                    <MySelectInput
                      name="role"
                      label="Role"
                      options={userRoleOptions}
                    />
                  </Grid.Column>
                </Grid.Row>
                <Grid.Row className="zero top padding">
                  <Grid.Column mobile={16} computer={5}>
                    <MySelectInput
                      name="state"
                      label="State"
                      options={userStateOptions}
                    />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={10}>
                    <MySelectInput
                      name="cities"
                      label="Cities"
                      options={userCitiesOptions}
                    />
                  </Grid.Column>
                </Grid.Row>
                <Grid.Row className="zero top padding">
                  <Grid.Column mobile={16} computer={5}>
                    <MySelectInput
                      name="freeTrialDurationMonths"
                      label="Free Trial Duration"
                      options={freeTrialDurationOptions}
                    />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={5}>
                    <MySelectInput
                      name="managerId"
                      label="Manager"
                      options={managerOptions}
                    />
                  </Grid.Column>
                </Grid.Row>

                <Grid.Row className="zero top padding"></Grid.Row>
              </Grid>

              <Header color="blue">Agent/Broker Details</Header>
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput
                      name="brokerageForms"
                      label="Brokerage Forms"
                    />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput
                      name="brokerageLogoRef"
                      label="Brokerage Logo Ref"
                    />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={6}>
                    <p className="bold">Has Brokerage Logo</p>
                    <MyRadioButtons
                      name="hasBrokerageLogo"
                      options={userBrokerageLogoOptions}
                    />
                    <LogoPreview />
                  </Grid.Column>
                </Grid.Row>
                <Grid.Row className="zero top padding">
                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput name="brokerageName" label="Brokerage Name" />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput name="brokerageId" label="Brokerage ID" />
                  </Grid.Column>

                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput
                      name="brokerageLicenseNumber"
                      label="Brokerage License Number"
                    />
                  </Grid.Column>
                </Grid.Row>
                <Grid.Row className="zero top padding">
                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput
                      name="brokerLicenseNumber"
                      label="Agent License Number"
                    />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput name="brokerNrdsId" label="Agent NRDS ID" />
                  </Grid.Column>
                </Grid.Row>
                <Grid.Row className="zero top padding">
                  <Grid.Column mobile={16} computer={8}>
                    <MySelectInput
                      name={"mlsAccessOptions"}
                      label="MLS Access (Select multiple)"
                      options={mlsAccessOptions}
                      multiple={true}
                    ></MySelectInput>
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={8}>
                    <MyMultiSelectInput
                      name="additionalFormsAccess"
                      label="Additional Forms Access (Select multiple)"
                      options={additionalFormsAccessOptions}
                    />
                  </Grid.Column>
                </Grid.Row>
              </Grid>
              <Header color="blue">Brokerage Address</Header>
              <FormAddress />

              <Header color="blue">Additional Information</Header>
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <MyTextInput name="notes" label="Notes" />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={8}>
                    <MyTextInput
                      name="referral"
                      label={
                        <span>
                          Referral{" "}
                          <Popup
                            trigger={<Icon name="info circle" color="blue" />}
                            content="Put first and last name of referral and deal e.g., Angie Agentname $50 referral when agent pays annual"
                            position="top center"
                          />
                        </span>
                      }
                    />
                  </Grid.Column>
                </Grid.Row>
              </Grid>
              <br />
              <Button
                loading={isSubmitting}
                disabled={!dirty || isSubmitting}
                type="submit"
                floated={isMobile ? "left" : "right"}
                primary
                content="Add New User"
                className={isMobile ? "fluid large" : "large"}
              />
            </Form>
          )}
        </Formik>
      </Segment>
      <CsvUserUpload />
      <Segment clearing>
        <div className="ui form medium margin bottom">
          <Header size="huge" color="blue">
            View All Users With Custom Claims
          </Header>
          <Divider />
          <Grid>
            <Grid.Row>
              <Grid.Column mobile={16} computer={16}>
                <Button
                  primary
                  onClick={() => {
                    handleViewAllCustomClaims();
                  }}
                  size="medium"
                  style={{ marginBottom: 10 }}
                  content="View"
                />
              </Grid.Column>
              <Grid.Column mobile={16} computer={4}>
                <p className="bold text blue">TCs</p>
                {usersWithCustomClaims?.tcs?.map(function (user) {
                  return (
                    <p>
                      {user.firstName} {user.lastName} {user.email}
                    </p>
                  );
                })}
              </Grid.Column>
              <Grid.Column mobile={16} computer={4}>
                <p className="bold text blue">Assistants</p>
                {usersWithCustomClaims?.assistants?.map(function (user) {
                  return (
                    <p>
                      {user.firstName} {user.lastName} {user.email}{" "}
                      {user.authCustomClaims.a[0]} {user.authCustomClaims.a[1]}
                    </p>
                  );
                })}
              </Grid.Column>
              <Grid.Column mobile={16} computer={4}>
                <p className="bold text blue">Managers</p>
                {usersWithCustomClaims?.managers?.map(function (user) {
                  return (
                    <p>
                      {user.firstName} {user.lastName} {user.email}
                    </p>
                  );
                })}
              </Grid.Column>
              <Grid.Column mobile={16} computer={4}>
                <p className="bold text blue">Admins</p>
                {usersWithCustomClaims?.admins?.map(function (user) {
                  return (
                    <p>
                      {user.firstName} {user.lastName} {user.email}
                    </p>
                  );
                })}
              </Grid.Column>
            </Grid.Row>
          </Grid>
        </div>
      </Segment>
      <Segment clearing>
        <Formik
          initialValues={{
            email: "",
            role: "",
            agentAssistingEmail: "",
            action: "none",
          }}
          validationSchema={Yup.object({
            email: Yup.string().required().email(),
          })}
          onSubmit={(values, { setSubmitting, setFieldValue }) => {
            switch (values.action) {
              case FormAction.authView:
                handleViewUserRole(values);
                break;
              case FormAction.authSet:
                handleAuthRole(values);
                break;
              case FormAction.authAssist:
                handleAuthAssistant(values);
                break;
              case FormAction.authAssistDelete:
                handleAuthAssistDelete(values);
                break;
              case FormAction.authNone:
                break;
              default:
                console.log("should never be called");
            }
            setSubmitting(false);
          }}
        >
          {({
            values,
            handleChange,
            handleSubmit,
            isSubmitting,
            isValid,
            dirty,
            errors,
          }) => (
            <Form className="ui form medium margin bottom">
              <Header size="huge" color="blue">
                Create User Roles
              </Header>
              <p className="bold text-medium">
                All TCs, Assistants, Manager Assistants and Managers MUST have
                their role set here in order to work properly on the site.
              </p>
              <p>
                1. Enter their email and click "View User Auth Role.
                <br />
                2. If the role is incorrect, select the correct role and click
                "Set User Auth Role".
                <br />
                3. If the role is Assistant, then you also need to use the right
                column and add each email of the agent they assist - one at a
                time.
                <br />
                4. If the role is Manager Assistant, then you also need to use
                the separate section below "Create manager Assistant Roles"
                where you designate the Manager they assist and their title they
                prefer.
                <br />
                5. If the role is Admin, we must update the database manually
                with isAdmin.
              </p>
              <Divider />
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <Grid.Column width={4}>
                      <MyTextInput
                        name="email"
                        type="email"
                        label="Email"
                        placeholder="Email address"
                        // onChange={handleChange}
                        value={values.email}
                      />
                      <MySelectInput
                        name="role"
                        label="Role"
                        options={roleOptions}
                      ></MySelectInput>

                      {errors.auth && (
                        <Label
                          basic
                          color="red"
                          style={{ marginBottom: 10 }}
                          content={errors.auth}
                        />
                      )}
                    </Grid.Column>

                    <Grid.Column>
                      <br />
                      <Button
                        name="button"
                        value={FormAction.authView}
                        primary
                        type="submit"
                        onChange={handleChange}
                        loading={isSubmitting}
                        disabled={
                          !isValid || !dirty || isSubmitting || !values.email
                        }
                        size="medium"
                        style={{ marginBottom: 10 }}
                        content="View User Auth Role"
                        onClick={() => {
                          values.action = FormAction.authView;
                          handleSubmit();
                        }}
                      />
                      <br />
                      <Button
                        name="button"
                        value={FormAction.authSet}
                        primary
                        type="button"
                        onChange={handleChange}
                        loading={isSubmitting}
                        disabled={
                          !isValid || !dirty || isSubmitting || !values.role
                        }
                        onClick={() => {
                          values.action = FormAction.authSet;
                          handleSubmit();
                        }}
                        size="medium"
                        style={{ marginBottom: 10 }}
                        content="Set User Auth Role"
                      />
                    </Grid.Column>
                  </Grid.Column>

                  <Grid.Column mobile={16} computer={8}>
                    <h3>Set Assisting Agent</h3>
                    <p>
                      To add an agent for this user to assist as an 'assistant',
                      you need to also fill out the role here in the left column
                      as "Assistant".
                      {/* <br />
                      Does this actually work? Note: TCs are only added here as somewhat 'permanent' TCs
                      for all transactions for a particular user.{" "}
                      <b>
                        This enables a TC to create a new transaction for an
                        agent.
                      </b>
                      <br />
                      If the TC only assists on particular transactions, then
                      the agent adds them as a party to the transaction. */}
                    </p>

                    <MyTextInput
                      name="agentAssistingEmail"
                      type="text"
                      label="Email of Agent To Assist"
                      value={values.agentAssistingEmail}
                    />
                    <Button
                      name="button"
                      value={FormAction.authAssist}
                      primary
                      type="submit"
                      onChange={handleChange}
                      loading={isSubmitting}
                      disabled={
                        !isValid ||
                        !dirty ||
                        isSubmitting ||
                        !values.agentAssistingEmail ||
                        !values.role
                      }
                      onClick={() => {
                        values.action = FormAction.authAssist;
                        handleSubmit();
                      }}
                      size="medium"
                      style={{ marginBottom: 10 }}
                      content="Add agent to who this user assists"
                    />
                    <Button
                      name="button"
                      value={FormAction.authAssistDelete}
                      primary
                      type="submit"
                      onChange={handleChange}
                      loading={isSubmitting}
                      disabled={
                        !isValid ||
                        !dirty ||
                        isSubmitting ||
                        !values.agentAssistingEmail
                      }
                      onClick={() => {
                        values.action = FormAction.authAssistDelete;
                        handleSubmit();
                      }}
                      size="medium"
                      style={{ marginBottom: 10 }}
                      content="Remove an agent from who this user assists"
                    />
                  </Grid.Column>
                </Grid.Row>
                <Grid.Row>
                  Console Log:
                  <div>
                    <ul>
                      {authLog.map(function (name, index) {
                        return <li key={index}>{name}</li>;
                      })}
                    </ul>
                  </div>
                </Grid.Row>
              </Grid>
            </Form>
          )}
        </Formik>
      </Segment>
      <Segment clearing>
        <Formik
          initialValues={{
            email: "",
            role: ROLE_TYPE_VALUES.MANAGER_ASSISTANT,
            managerEmail: "",
            title: "Manager Assistant",
            action: "none",
          }}
          validationSchema={Yup.object({
            email: Yup.string().required().email(),
            managerEmail: Yup.string().required().email(),
            title: Yup.string().required(),
          })}
          onSubmit={(values, { setSubmitting, setFieldValue }) => {
            switch (values.action) {
              case "managerAssistAdd":
                handleAuthManagerAssistant(values);
                break;
              case "managerAssistDelete":
                handleAuthManagerAssistDelete(values);
                break;
              default:
                console.log("should never be called");
            }
            setSubmitting(false);
          }}
        >
          {({
            values,
            handleChange,
            handleSubmit,
            isSubmitting,
            isValid,
            dirty,
            errors,
          }) => (
            <Form className="ui form medium margin bottom">
              <Header size="huge" color="blue">
                Create Manager Assistant Roles
              </Header>
              <p className="bold text-medium">
                Manager Assistants can see and do everything that a manager can
                do, except for financials.
              </p>
              <p>
                The Title is their personal preference for their role and
                appears when assigning tasks.
              </p>
              <Divider />
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <MyTextInput
                      name="email"
                      type="email"
                      label="Manager Assistant Email"
                      placeholder="Email address"
                      value={values.email}
                    />
                    <MyTextInput
                      name="managerEmail"
                      type="email"
                      label="Manager Email"
                      placeholder="Manager email address"
                      value={values.managerEmail}
                    />
                    <MyTextInput
                      name="title"
                      type="text"
                      label="Title"
                      placeholder="Manager Assistant"
                      value={values.title}
                    />
                    {/* <MyCheckbox
                      name="sendEmailNotificationsOfNewTransactions"
                      label="Send Email Notifications of New Transactions"
                    /> */}
                    <Button
                      name="button"
                      value="managerAssistAdd"
                      primary
                      type="submit"
                      onChange={handleChange}
                      loading={isSubmitting}
                      disabled={
                        !isValid ||
                        !dirty ||
                        isSubmitting ||
                        !values.managerEmail ||
                        !values.email
                      }
                      onClick={() => {
                        values.action = "managerAssistAdd";
                        handleSubmit();
                      }}
                      size="medium"
                      style={{ marginBottom: 10 }}
                      content="Add Manager Assistant"
                    />
                    <Button
                      name="button"
                      value="managerAssistDelete"
                      primary
                      type="submit"
                      onChange={handleChange}
                      loading={isSubmitting}
                      disabled={
                        !isValid ||
                        !dirty ||
                        isSubmitting ||
                        !values.managerEmail ||
                        !values.email
                      }
                      onClick={() => {
                        values.action = "managerAssistDelete";
                        handleSubmit();
                      }}
                      size="medium"
                      style={{ marginBottom: 10 }}
                      content="Remove Manager Assistant"
                    />
                  </Grid.Column>
                </Grid.Row>
              </Grid>
            </Form>
          )}
        </Formik>
      </Segment>
      <Segment clearing>
        <Formik
          initialValues={{
            managerEmail: "",
            sendEmailNotifications: true,
          }}
          validationSchema={Yup.object({
            managerEmail: Yup.string().required().email(),
          })}
          onSubmit={(values, { setSubmitting }) => {
            handleUpdateManagerDetails(values);
            setSubmitting(false);
          }}
        >
          {({
            values,
            handleChange,
            handleSubmit,
            isSubmitting,
            isValid,
            dirty,
            errors,
          }) => (
            <Form className="ui form medium margin bottom">
              <Header size="huge" color="blue">
                Update Manager Details
              </Header>
              <p>
                Save Manager Email Notifications to each Agent Profile, such
                that the manager will receive an email everytime one of their
                agents creates a new transaction.
              </p>
              <Divider />
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <MyTextInput
                      name="managerEmail"
                      type="email"
                      label="Manager Email"
                      placeholder="Manager email address"
                      value={values.managerEmail}
                    />

                    <div className="field">
                      <div className="ui checkbox">
                        <input
                          type="checkbox"
                          name="sendEmailNotifications"
                          checked={values.sendEmailNotifications}
                          onChange={handleChange}
                        />
                        <label>
                          Send Email Notifications of New Transactions
                        </label>
                      </div>
                    </div>
                    <Button
                      primary
                      type="submit"
                      loading={isSubmitting}
                      disabled={!isValid || !dirty || isSubmitting}
                      size="medium"
                      style={{ marginBottom: 10, marginTop: 15 }}
                      content="Update Manager Details"
                    />
                  </Grid.Column>
                </Grid.Row>
              </Grid>
            </Form>
          )}
        </Formik>
      </Segment>
      <Segment clearing>
        <Formik
          initialValues={{
            email: "",
            emailNew: "",
          }}
          validationSchema={Yup.object({
            email: Yup.string().required().email(),
            emailNew: Yup.string().required().email(),
          })}
          onSubmit={(values, { setSubmitting, setFieldValue }) => {
            handleUpdateUserEmail(values);
            setSubmitting(false);
          }}
        >
          {({
            values,
            handleChange,
            handleSubmit,
            isSubmitting,
            isValid,
            dirty,
            errors,
          }) => (
            <Form className="ui form medium margin bottom">
              <Header size="huge" color="blue">
                Update User Email
              </Header>
              <p>
                This will update the user's email in Auth, Users and
                Transactions collections.
                <br />
                It works for any user (agent, tc, manager, buyers/sellers).
                <br />
                For buyer/seller clients, the agent can update their email
                themselves inside the transaction in the Parties section.
              </p>
              <Divider />
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <Grid.Column width={4}>
                      <MyTextInput
                        name="email"
                        type="email"
                        label="Email"
                        placeholder="Old Email address"
                      />
                      <MyTextInput
                        name="emailNew"
                        type="email"
                        label="Email New"
                        placeholder="New Email address"
                      />
                      {errors.auth && (
                        <Label
                          basic
                          color="red"
                          style={{ marginBottom: 10 }}
                          content={errors.auth}
                        />
                      )}
                    </Grid.Column>

                    <Grid.Column>
                      <br />
                      <Button
                        name="button"
                        value={FormAction.authSet}
                        primary
                        type="button"
                        onChange={handleChange}
                        loading={isSubmitting}
                        disabled={!isValid || !dirty || isSubmitting}
                        onClick={() => {
                          handleSubmit();
                        }}
                        size="medium"
                        style={{ marginBottom: 10 }}
                        content="Update User Email"
                      />
                    </Grid.Column>
                  </Grid.Column>
                </Grid.Row>
              </Grid>
            </Form>
          )}
        </Formik>
      </Segment>
      <Segment clearing>
        <Formik
          initialValues={{
            logoStoragePath: "",
          }}
          validationSchema={Yup.object({
            logoStoragePath: Yup.string().required(),
          })}
          onSubmit={(values, { setSubmitting, setFieldValue }) => {
            dispatch(
              openModal({
                modalType: "AdminLogoPhotoUpload",
                modalProps: { logoStoragePath: values.logoStoragePath },
              })
            );
            setSubmitting(false);
          }}
        >
          {({
            values,
            handleChange,
            handleSubmit,
            isSubmitting,
            isValid,
            dirty,
            errors,
          }) => (
            <Form className="ui form medium margin bottom">
              <Header size="huge" color="blue">
                Brokerage Photos
              </Header>
              <p>
                Upload a brokerage logo for the top of forms if one does not
                already exist for that brokerage.
                <br />
                Dimensions must be: 1000x150 pixels transparent png named
                "logo.png".
                <br />
                Storage Path needs to match brokerageForms
              </p>
              <Divider />
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <Grid.Column width={4}>
                      <MyTextInput
                        name="logoStoragePath"
                        type="text"
                        label="Logo Storage Path"
                        placeholder="Examples: CB/DistinctiveProperties"
                      />
                      {errors.auth && (
                        <Label
                          basic
                          color="red"
                          style={{ marginBottom: 10 }}
                          content={errors.auth}
                        />
                      )}
                    </Grid.Column>

                    <Grid.Column>
                      <br />
                      <Button
                        name="button"
                        value={FormAction.authSet}
                        primary
                        type="submit"
                        onChange={handleChange}
                        loading={isSubmitting}
                        disabled={!isValid || !dirty || isSubmitting}
                        size="medium"
                        style={{ marginBottom: 10 }}
                        content="Upload Photo"
                      />
                    </Grid.Column>
                  </Grid.Column>
                </Grid.Row>
              </Grid>
            </Form>
          )}
        </Formik>
      </Segment>
      <Segment clearing>
        <Formik
          initialValues={{
            email: "",
            brokerageForms: "",
            brokerageLogoRef: "",
            hasBrokerageLogo: "true",
            brokerageName: "",
            brokerageLicenseNumber: "",
            address: {
              street: "",
              unit: "",
              city: "",
              state: "",
              zipcode: "",
            },
          }}
          validationSchema={Yup.object({
            email: Yup.string().required().email(),
          })}
          onSubmit={async (values, { setSubmitting }) => {
            try {
              // First, get the user profile by email to get the user ID
              const userProfile = await getUserProfileByEmailFromDb(
                values.email
              );
              if (!userProfile || userProfile.length === 0) {
                toast.error("User not found with that email address");
                setSubmitting(false);
                return;
              }

              // Convert brokerageLogoRef to full URL if it's not empty
              const fullLogoUrl = values.brokerageLogoRef
                ? `https://storage.googleapis.com/transact-staging.appspot.com/brokerageLogos/${values.brokerageLogoRef}/logo.png`
                : "";
              // Update user profile with new brokerage information using user ID
              await updateProfileFieldsInDb(userProfile[0].id, {
                brokerageForms: values.brokerageForms,
                brokerageLogoRef: fullLogoUrl,
                hasBrokerageLogo: values.hasBrokerageLogo === "true",
                brokerageName: values.brokerageName,
                brokerageLicenseNumber: values.brokerageLicenseNumber,
                address: values.address,
              });
              toast.success(
                "Agent brokerage information updated successfully!"
              );
            } catch (error) {
              console.error("Error updating agent brokerage:", error);
              toast.error("Error updating agent brokerage information");
            }
            setSubmitting(false);
          }}
        >
          {({ isSubmitting, dirty, isValid, values }) => (
            <Form className="ui form medium margin bottom">
              <Header size="huge" color="blue">
                Agent Changed Brokerages
              </Header>
              <p style={{ marginBottom: "20px" }}>
                These new brokerage fields are used for new transactions only
                and does not affect the existing transactions as the existing
                transactions are owned by your previous brokerage.
                <br /> <br />
                Verify Brokerage Forms and Logo Ref are correct by hovering over
                Logo Preview.
              </p>
              <Divider />

              <Header color="blue">Agent Email</Header>
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <MyEmailInput name="email" label="Agent Email" />
                  </Grid.Column>
                </Grid.Row>
              </Grid>

              <Header color="blue">New Brokerage Details</Header>
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput
                      name="brokerageForms"
                      label="Brokerage Forms"
                    />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={5}>
                    <MyTextInput
                      name="brokerageLogoRef"
                      label="Brokerage Logo Ref"
                    />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={5}>
                    <p className="bold">Has Brokerage Logo</p>
                    <MyRadioButtons
                      name="hasBrokerageLogo"
                      options={userBrokerageLogoOptions}
                    />
                    <LogoPreview />
                  </Grid.Column>
                </Grid.Row>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={8}>
                    <MyTextInput name="brokerageName" label="Brokerage Name" />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={8}>
                    <MyTextInput
                      name="brokerageLicenseNumber"
                      label="Brokerage License Number"
                    />
                  </Grid.Column>
                </Grid.Row>
              </Grid>

              <Header color="blue">New Brokerage Address</Header>
              <FormAddress />
              <br />
              <Button
                loading={isSubmitting}
                disabled={!dirty || !isValid}
                type="submit"
                size="large"
                primary
              >
                Update Agent Brokerage Information
              </Button>
            </Form>
          )}
        </Formik>
      </Segment>
      {(currentUserProfile.id === "RgzoxvghPqb2jRfQOqXgppG1rJm2" ||
        currentUserProfile.id === "wrdn74w4vYTI281g7KJtXjJYBBg2" ||
        currentUserProfile.id === "pStfFKH7g5T4PC3J8ECXKffu3ts1" ||
        currentUserProfile.id === "QVKdrTFDy9fP0t9ndImXcUhvb0p1") && (
        <>
          <Segment clearing>
            <Formik
              initialValues={{
                selectedState: "Colorado",
                selectedForm: "",
              }}
              onSubmit={async (values, { setSubmitting, resetForm }) => {
                try {
                  setSubmitting(true);
                  setExpireFormMessage(""); // Clear previous message
                  const result = await handleExpireForm(
                    values.selectedState,
                    values.selectedForm
                  );

                  if (result.success) {
                    setExpireFormMessage(result.message);
                    setExpireFormMessageType("success");
                    resetForm();
                  } else {
                    setExpireFormMessage(result.message);
                    setExpireFormMessageType("error");
                  }
                } catch (error) {
                  console.error("Error expiring form:", error);
                  setExpireFormMessage(
                    "An unexpected error occurred. Please try again."
                  );
                  setExpireFormMessageType("error");
                } finally {
                  setSubmitting(false);
                }
              }}
            >
              {({ isSubmitting, values, setFieldValue }) => (
                <Form className="ui form">
                  <Header size="huge" color="blue">
                    Expire a Form and All Matching Form Templates
                  </Header>
                  <Divider />
                  <Grid>
                    <Grid.Row>
                      <Grid.Column mobile={16} computer={8}>
                        <MySelectInput
                          name="selectedState"
                          label="Select State"
                          options={userStateOptions}
                          onChange={(e, { value }) => {
                            setFieldValue("selectedState", value);
                            setFieldValue("selectedForm", ""); // Reset form selection when state changes
                          }}
                        />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={8}>
                        <ExpireFormSelect
                          selectedState={values.selectedState}
                          selectedForm={values.selectedForm}
                          onFormChange={(value) =>
                            setFieldValue("selectedForm", value)
                          }
                        />
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row>
                      <Grid.Column mobile={16} computer={16}>
                        <Button
                          loading={isSubmitting}
                          disabled={!values.selectedForm || isSubmitting}
                          type="submit"
                          size="large"
                          color="red"
                        >
                          Expire Form and All Matching Templates
                        </Button>
                        {expireFormMessage && (
                          <div style={{ marginTop: "10px" }}>
                            <div
                              className={`ui message ${
                                expireFormMessageType === "success"
                                  ? "success"
                                  : "error"
                              }`}
                            >
                              {expireFormMessage}
                            </div>
                          </div>
                        )}
                      </Grid.Column>
                    </Grid.Row>
                  </Grid>
                </Form>
              )}
            </Formik>
          </Segment>
          <Segment clearing>
            <div className="ui form medium margin bottom">
              <Header size="huge" color="blue">
                View FormField Conversions From Database
              </Header>
              <Divider />
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={16}>
                    <Button
                      primary
                      onClick={() => {
                        handleViewFormFieldConversions();
                      }}
                      size="medium"
                      style={{ marginRight: 20 }}
                      content="View"
                    />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={16}>
                    {formFieldConversionArray &&
                      formFieldConversionArray.forEach((formField) => (
                        <>
                          {formField.title && <p>{formField.title}</p>}
                          {formField.convertTo && <p>{formField.convertTo}</p>}
                          {formField.pdfFieldName && (
                            <p>{formField.pdfFieldName}</p>
                          )}
                        </>
                      ))}
                  </Grid.Column>
                </Grid.Row>
              </Grid>
            </div>
          </Segment>
          <Segment clearing>
            <div className="ui form medium margin bottom">
              <Header size="huge" color="blue">
                DataFiniti MLS Views
              </Header>
              <Divider />
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={16}>
                    <Button
                      primary
                      onClick={() => {
                        handleGetDataFinitiViews();
                      }}
                      size="medium"
                      style={{ marginRight: 20 }}
                      content="See Views"
                    />
                    <Button
                      primary
                      onClick={() => {
                        handleCreateDataFinitiView();
                      }}
                      size="medium"
                      style={{ marginBottom: 10 }}
                      content="Create View"
                    />
                  </Grid.Column>
                </Grid.Row>
              </Grid>
            </div>
          </Segment>
        </>
      )}

      {/* Edit User Modal */}
      {showEditUserModal && editingUser && (
        <Modal
          open={showEditUserModal}
          onClose={handleCloseEditModal}
          size="large"
          closeIcon
        >
          <Modal.Header>Edit User</Modal.Header>
          <Modal.Content>
            <Formik
              enableReinitialize
              initialValues={{
                id: editingUser.id || "",
                email: editingUser.email || "",
                role: editingUser.role || "agent",
                firstName: editingUser.firstName || "",
                lastName: editingUser.lastName || "",
                middleName: editingUser.middleName || "",
                phone: editingUser.phone || "",
                state: editingUser.state || "Colorado",
                cities: editingUser.cities || "",
                brokerageForms: editingUser.brokerageForms || "",
                hasBrokerageLogo: editingUser.hasBrokerageLogo
                  ? "true"
                  : "false",
                // Extract just the part between brokerageLogos/ and /logo.png
                brokerageLogoRef: editingUser.brokerageLogoRef
                  ? editingUser.brokerageLogoRef.includes("brokerageLogos/")
                    ? editingUser.brokerageLogoRef
                        .split("brokerageLogos/")[1]
                        ?.split("/logo.png")[0] || ""
                    : editingUser.brokerageLogoRef
                  : "",
                brokerageName: editingUser.brokerageName || "",
                brokerageId: editingUser.brokerageId || "",
                brokerageLicenseNumber:
                  editingUser.brokerageLicenseNumber || "",
                brokerLicenseNumber: editingUser.brokerLicenseNumber || "",
                brokerNrdsId: editingUser.brokerNrdsId || "",
                mlsAccessOptions:
                  editingUser.mlsAccess?.map((mls) => mls.mlsIdCode) || [],
                additionalFormsAccess: editingUser.additionalFormsAccess || [],
                managerId: editingUser.managerId || "",
                notes: editingUser.notes || "",
                referral: editingUser.referral || "",
                // stateProvince as multi-select array
                stateProvinceOptions:
                  editingUser.stateProvince?.map(
                    (sp) => sp.stateProvinceCode
                  ) || [],
                type: editingUser.type || "user",
                address: {
                  street: editingUser.address?.street || "",
                  unit: editingUser.address?.unit || "",
                  city: editingUser.address?.city || "",
                  state: editingUser.address?.state || "",
                  zipcode: editingUser.address?.zipcode || "",
                },
                // Latest payment info instead of free trial duration
                latestPayment:
                  editingUser.payments && editingUser.payments.length > 0
                    ? editingUser.payments.reduce((latest, payment) => {
                        if (!payment.dateExpires) return latest;
                        const paymentDate = payment.dateExpires.toDate
                          ? payment.dateExpires.toDate()
                          : new Date(payment.dateExpires);
                        const latestDate = latest.dateExpires
                          ? latest.dateExpires.toDate
                            ? latest.dateExpires.toDate()
                            : new Date(latest.dateExpires)
                          : new Date(0);
                        return paymentDate > latestDate ? payment : latest;
                      }, {})
                    : { amount: "", datePayment: "", dateExpires: "" },
              }}
              validationSchema={Yup.object({
                email: Yup.string().required().email(),
                role: Yup.string().required(),
                firstName: Yup.string().required(),
                lastName: Yup.string().required(),
                state: Yup.string().required(),
                brokerageForms: Yup.string().required(),
                type: Yup.string().required(),
                stateProvinceOptions: Yup.array().min(
                  1,
                  "At least one state/province is required"
                ),
              })}
              onSubmit={async (values, { setSubmitting }) => {
                await handleUpdateUser(values);
                setSubmitting(false);
              }}
            >
              {({ isSubmitting, isValid, values }) => (
                <Form className="ui form">
                  <Header color="blue">Name & Contact Info</Header>
                  <Grid>
                    <Grid.Row>
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput name="firstName" label="First name" />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput name="middleName" label="Middle name" />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput name="lastName" label="Last name" />
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="zero top padding">
                      <Grid.Column mobile={16} computer={5}>
                        <MyEmailInput name="email" label="Email" />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput name="phone" label="Phone" />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={5}>
                        <MySelectInput
                          name="role"
                          label="Role"
                          options={userRoleOptions}
                        />
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="zero top padding">
                      <Grid.Column mobile={16} computer={5}>
                        <MySelectInput
                          name="state"
                          label="State"
                          options={userStateOptions}
                        />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={10}>
                        <MySelectInput
                          name="cities"
                          label="Cities"
                          options={userCitiesOptions}
                        />
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="zero top padding">
                      <Grid.Column mobile={16} computer={5}>
                        <MySelectInput
                          name="stateProvinceOptions"
                          label="State/Province (Select multiple)"
                          options={stateProvinceDropdownOptions}
                          multiple={true}
                        />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={5}>
                        <MySelectInput
                          name="type"
                          label="Type"
                          options={userTypeOptions}
                        />
                      </Grid.Column>
                    </Grid.Row>
                  </Grid>

                  <Header color="blue">Latest Payment Information</Header>
                  <Grid>
                    <Grid.Row>
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput
                          name="latestPayment.amount"
                          label="Amount"
                          readOnly
                          value={
                            values.latestPayment?.amount || "No payment found"
                          }
                        />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput
                          name="latestPayment.datePayment"
                          label="Payment Date"
                          readOnly
                          value={
                            values.latestPayment?.datePayment
                              ? values.latestPayment.datePayment.toDate
                                ? values.latestPayment.datePayment
                                    .toDate()
                                    .toLocaleDateString()
                                : new Date(
                                    values.latestPayment.datePayment
                                  ).toLocaleDateString()
                              : "N/A"
                          }
                        />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput
                          name="latestPayment.dateExpires"
                          label="Expires Date"
                          readOnly
                          value={
                            values.latestPayment?.dateExpires
                              ? values.latestPayment.dateExpires.toDate
                                ? values.latestPayment.dateExpires
                                    .toDate()
                                    .toLocaleDateString()
                                : new Date(
                                    values.latestPayment.dateExpires
                                  ).toLocaleDateString()
                              : "N/A"
                          }
                        />
                      </Grid.Column>
                    </Grid.Row>
                  </Grid>

                  <Header color="blue">Agent/Broker Details</Header>
                  <Grid>
                    <Grid.Row>
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput
                          name="brokerageForms"
                          label="Brokerage Forms"
                        />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput
                          name="brokerageLogoRef"
                          label="Brokerage Logo Ref"
                        />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={6}>
                        <p className="bold">Has Brokerage Logo</p>
                        <MyRadioButtons
                          name="hasBrokerageLogo"
                          options={userBrokerageLogoOptions}
                        />
                        <LogoPreview />
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="zero top padding">
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput
                          name="brokerageName"
                          label="Brokerage Name"
                        />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput name="brokerageId" label="Brokerage ID" />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput
                          name="brokerageLicenseNumber"
                          label="Brokerage License Number"
                        />
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="zero top padding">
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput
                          name="brokerLicenseNumber"
                          label="Agent License Number"
                        />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={5}>
                        <MyTextInput
                          name="brokerNrdsId"
                          label="Agent NRDS ID"
                        />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={5}>
                        <MySelectInput
                          name="managerId"
                          label="Manager"
                          options={managerOptions}
                        />
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="zero top padding">
                      <Grid.Column mobile={16} computer={8}>
                        <MySelectInput
                          name={"mlsAccessOptions"}
                          label="MLS Access (Select multiple)"
                          options={mlsAccessOptions}
                          multiple={true}
                        ></MySelectInput>
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={8}>
                        <MyMultiSelectInput
                          name="additionalFormsAccess"
                          label="Additional Forms Access (Select multiple)"
                          options={additionalFormsAccessOptions}
                        />
                      </Grid.Column>
                    </Grid.Row>
                  </Grid>
                  <Header color="blue">Brokerage Address</Header>
                  <FormAddress />

                  <Header color="blue">Additional Information</Header>
                  <Grid>
                    <Grid.Row>
                      <Grid.Column mobile={16} computer={8}>
                        <MyTextInput name="notes" label="Notes" />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={8}>
                        <MyTextInput name="referral" label="Referral" />
                      </Grid.Column>
                    </Grid.Row>
                  </Grid>
                  <br />
                  <Button
                    loading={isSubmitting}
                    disabled={isSubmitting}
                    type="submit"
                    floated="right"
                    primary
                    content="Save Changes"
                    className="large"
                  />
                </Form>
              )}
            </Formik>
          </Modal.Content>
          <Modal.Actions>
            <Button onClick={handleCloseEditModal}>Cancel</Button>
          </Modal.Actions>
        </Modal>
      )}
    </div>
  );
}
